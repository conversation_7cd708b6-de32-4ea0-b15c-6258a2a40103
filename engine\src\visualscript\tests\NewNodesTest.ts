/**
 * 新节点测试
 * 测试第三批次和第四批次新增的节点功能
 */
import { NodeRegistry } from '../nodes/NodeRegistry';
import { ExecutionContext } from '../execution/ExecutionContext';
import { Graph } from '../graph/Graph';
import { World } from '../../core/World';

// 导入新节点注册函数
import { registerDebugNodes } from '../presets/DebugNodes';
import { registerNetworkSecurityNodes } from '../presets/NetworkSecurityNodes';
import { registerWebRTCNodes } from '../presets/WebRTCNodes';
import { registerAIEmotionNodes } from '../presets/AIEmotionNodes';
import { registerAINLPNodes } from '../presets/AINLPNodes';
import { registerNetworkProtocolNodes } from '../presets/NetworkProtocolNodes';
import { registerStringNodes } from '../presets/StringNodes';
import { registerArrayNodes } from '../presets/ArrayNodes';
import { registerObjectNodes } from '../presets/ObjectNodes';
import { registerVariableNodes } from '../presets/VariableNodes';

/**
 * 新节点测试类
 */
export class NewNodesTest {
  private registry: NodeRegistry;
  private world: World;
  private graph: Graph;
  private context: ExecutionContext;

  constructor() {
    this.registry = new NodeRegistry();
    this.world = new World();
    this.graph = new Graph();
    this.context = new ExecutionContext(this.world, this.graph);
    
    // 注册所有新节点
    this.registerAllNewNodes();
  }

  /**
   * 注册所有新节点
   */
  private registerAllNewNodes(): void {
    // 第三批次节点
    registerDebugNodes(this.registry);
    registerNetworkSecurityNodes(this.registry);
    registerWebRTCNodes(this.registry);
    registerAIEmotionNodes(this.registry);
    registerAINLPNodes(this.registry);
    registerNetworkProtocolNodes(this.registry);

    // 第四批次节点
    registerStringNodes(this.registry);
    registerArrayNodes(this.registry);
    registerObjectNodes(this.registry);
    registerVariableNodes(this.registry);
  }

  /**
   * 测试字符串操作节点
   */
  public testStringNodes(): boolean {
    console.log('测试字符串操作节点...');

    try {
      // 测试字符串连接节点
      const concatNode = this.registry.createNode('string/concat', {
        id: 'test-concat',
        type: 'string/concat',
        graph: this.graph,
        context: this.context
      });

      if (concatNode) {
        concatNode.setInputValue('string1', 'Hello');
        concatNode.setInputValue('string2', 'World');
        concatNode.setInputValue('separator', ' ');
        const result = concatNode.execute();
        
        if (result === 'Hello World') {
          console.log('✅ 字符串连接节点测试通过');
        } else {
          console.log('❌ 字符串连接节点测试失败:', result);
          return false;
        }
      }

      // 测试字符串长度节点
      const lengthNode = this.registry.createNode('string/length', {
        id: 'test-length',
        type: 'string/length',
        graph: this.graph,
        context: this.context
      });

      if (lengthNode) {
        lengthNode.setInputValue('string', 'Hello World');
        const result = lengthNode.execute();
        
        if (result === 11) {
          console.log('✅ 字符串长度节点测试通过');
        } else {
          console.log('❌ 字符串长度节点测试失败:', result);
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('字符串节点测试出错:', error);
      return false;
    }
  }

  /**
   * 测试数组操作节点
   */
  public testArrayNodes(): boolean {
    console.log('测试数组操作节点...');

    try {
      // 测试数组添加节点
      const pushNode = this.registry.createNode('array/push', {
        id: 'test-push',
        type: 'array/push',
        graph: this.graph,
        context: this.context
      });

      if (pushNode) {
        pushNode.setInputValue('array', [1, 2, 3]);
        pushNode.setInputValue('element', 4);
        const result = pushNode.execute();
        
        if (Array.isArray(result) && result.length === 4 && result[3] === 4) {
          console.log('✅ 数组添加节点测试通过');
        } else {
          console.log('❌ 数组添加节点测试失败:', result);
          return false;
        }
      }

      // 测试数组长度节点
      const lengthNode = this.registry.createNode('array/length', {
        id: 'test-array-length',
        type: 'array/length',
        graph: this.graph,
        context: this.context
      });

      if (lengthNode) {
        lengthNode.setInputValue('array', [1, 2, 3, 4, 5]);
        const result = lengthNode.execute();
        
        if (result === 5) {
          console.log('✅ 数组长度节点测试通过');
        } else {
          console.log('❌ 数组长度节点测试失败:', result);
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('数组节点测试出错:', error);
      return false;
    }
  }

  /**
   * 测试对象操作节点
   */
  public testObjectNodes(): boolean {
    console.log('测试对象操作节点...');

    try {
      // 测试获取对象属性节点
      const getPropertyNode = this.registry.createNode('object/getProperty', {
        id: 'test-get-property',
        type: 'object/getProperty',
        graph: this.graph,
        context: this.context
      });

      if (getPropertyNode) {
        getPropertyNode.setInputValue('object', { name: 'Test', value: 42 });
        getPropertyNode.setInputValue('property', 'name');
        const result = getPropertyNode.execute();
        
        if (result === 'Test') {
          console.log('✅ 获取对象属性节点测试通过');
        } else {
          console.log('❌ 获取对象属性节点测试失败:', result);
          return false;
        }
      }

      // 测试对象键列表节点
      const keysNode = this.registry.createNode('object/keys', {
        id: 'test-keys',
        type: 'object/keys',
        graph: this.graph,
        context: this.context
      });

      if (keysNode) {
        keysNode.setInputValue('object', { a: 1, b: 2, c: 3 });
        const result = keysNode.execute();
        
        if (Array.isArray(result) && result.length === 3) {
          console.log('✅ 对象键列表节点测试通过');
        } else {
          console.log('❌ 对象键列表节点测试失败:', result);
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('对象节点测试出错:', error);
      return false;
    }
  }

  /**
   * 测试变量操作节点
   */
  public testVariableNodes(): boolean {
    console.log('测试变量操作节点...');

    try {
      // 测试设置变量节点
      const setNode = this.registry.createNode('variable/set', {
        id: 'test-set-var',
        type: 'variable/set',
        graph: this.graph,
        context: this.context
      });

      if (setNode) {
        setNode.setInputValue('name', 'testVar');
        setNode.setInputValue('value', 100);
        setNode.execute();
      }

      // 测试获取变量节点
      const getNode = this.registry.createNode('variable/get', {
        id: 'test-get-var',
        type: 'variable/get',
        graph: this.graph,
        context: this.context
      });

      if (getNode) {
        getNode.setInputValue('name', 'testVar');
        const result = getNode.execute();
        
        if (result === 100) {
          console.log('✅ 变量操作节点测试通过');
        } else {
          console.log('❌ 变量操作节点测试失败:', result);
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('变量节点测试出错:', error);
      return false;
    }
  }

  /**
   * 运行所有测试
   */
  public runAllTests(): boolean {
    console.log('开始运行新节点测试...\n');

    const results = [
      this.testStringNodes(),
      this.testArrayNodes(),
      this.testObjectNodes(),
      this.testVariableNodes()
    ];

    const allPassed = results.every(result => result);

    console.log('\n测试结果:');
    console.log(`字符串节点: ${results[0] ? '✅ 通过' : '❌ 失败'}`);
    console.log(`数组节点: ${results[1] ? '✅ 通过' : '❌ 失败'}`);
    console.log(`对象节点: ${results[2] ? '✅ 通过' : '❌ 失败'}`);
    console.log(`变量节点: ${results[3] ? '✅ 通过' : '❌ 失败'}`);
    console.log(`\n总体结果: ${allPassed ? '✅ 所有测试通过' : '❌ 部分测试失败'}`);

    return allPassed;
  }

  /**
   * 获取注册的节点统计
   */
  public getNodeStatistics(): any {
    const allTypes = this.registry.getAllNodeTypes();
    const categories = this.registry.getAllCategories();

    return {
      totalNodes: allTypes.length,
      categories: categories.length,
      nodesByCategory: categories.map(category => ({
        category,
        count: this.registry.getNodeTypesByCategory(category).length
      }))
    };
  }
}

// 导出测试函数
export function runNewNodesTest(): boolean {
  const test = new NewNodesTest();
  return test.runAllTests();
}

export function getNewNodesStatistics(): any {
  const test = new NewNodesTest();
  return test.getNodeStatistics();
}
