/**
 * 渲染系统节点
 * 提供相机创建和控制功能
 */
import * as THREE from 'three';
import { Node } from '../nodes/Node';
import { NodeCategory, SocketType, SocketDirection } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { Camera, CameraType } from '../../rendering/Camera';
import { Entity } from '../../core/Entity';
import { Transform } from '../../scene/Transform';

/**
 * 创建透视相机节点
 */
export class CreatePerspectiveCameraNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'fov',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 75,
      description: '视野角度'
    });

    this.addInput({
      name: 'aspect',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 16/9,
      description: '宽高比'
    });

    this.addInput({
      name: 'near',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 0.1,
      description: '近裁剪面'
    });

    this.addInput({
      name: 'far',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 1000,
      description: '远裁剪面'
    });

    this.addInput({
      name: 'entityName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      defaultValue: '透视相机',
      description: '实体名称'
    });

    // 输出插槽
    this.addOutput({
      name: 'onComplete',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成输出'
    });

    this.addOutput({
      name: 'camera',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Camera',
      description: '创建的相机组件'
    });

    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Entity',
      description: '相机实体'
    });
  }

  public execute(): any {
    try {
      // 获取输入值
      const fov = this.getInputValue('fov') || 75;
      const aspect = this.getInputValue('aspect') || (window.innerWidth / window.innerHeight);
      const near = this.getInputValue('near') || 0.1;
      const far = this.getInputValue('far') || 1000;
      const entityName = this.getInputValue('entityName') || '透视相机';

      // 创建相机实体
      const cameraEntity = new Entity(entityName);

      // 创建相机组件
      const camera = new Camera({
        type: CameraType.PERSPECTIVE,
        fov: fov,
        aspect: aspect,
        near: near,
        far: far,
        autoAspect: true
      });

      // 添加相机组件到实体
      cameraEntity.addComponent(camera);

      // 添加变换组件
      const transform = new Transform();
      cameraEntity.addComponent(transform);

      // 设置输出值
      this.setOutputValue('camera', camera);
      this.setOutputValue('entity', cameraEntity);

      // 触发完成流程
      this.triggerFlow('onComplete');

      return {
        camera: camera,
        entity: cameraEntity
      };
    } catch (error) {
      console.error('创建透视相机失败:', error);
      throw error;
    }
  }
}

/**
 * 创建正交相机节点
 */
export class CreateOrthographicCameraNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'left',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: -10,
      description: '左平面'
    });

    this.addInput({
      name: 'right',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 10,
      description: '右平面'
    });

    this.addInput({
      name: 'top',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 10,
      description: '上平面'
    });

    this.addInput({
      name: 'bottom',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: -10,
      description: '下平面'
    });

    this.addInput({
      name: 'near',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 0.1,
      description: '近裁剪面'
    });

    this.addInput({
      name: 'far',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      defaultValue: 1000,
      description: '远裁剪面'
    });

    this.addInput({
      name: 'entityName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      defaultValue: '正交相机',
      description: '实体名称'
    });

    // 输出插槽
    this.addOutput({
      name: 'onComplete',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成输出'
    });

    this.addOutput({
      name: 'camera',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Camera',
      description: '创建的相机组件'
    });

    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Entity',
      description: '相机实体'
    });
  }

  public execute(): any {
    try {
      // 获取输入值
      const left = this.getInputValue('left') || -10;
      const right = this.getInputValue('right') || 10;
      const top = this.getInputValue('top') || 10;
      const bottom = this.getInputValue('bottom') || -10;
      const near = this.getInputValue('near') || 0.1;
      const far = this.getInputValue('far') || 1000;
      const entityName = this.getInputValue('entityName') || '正交相机';

      // 创建相机实体
      const cameraEntity = new Entity(entityName);

      // 创建相机组件
      const camera = new Camera({
        type: CameraType.ORTHOGRAPHIC,
        left: left,
        right: right,
        top: top,
        bottom: bottom,
        near: near,
        far: far
      });

      // 添加相机组件到实体
      cameraEntity.addComponent(camera);

      // 添加变换组件
      const transform = new Transform();
      cameraEntity.addComponent(transform);

      // 设置输出值
      this.setOutputValue('camera', camera);
      this.setOutputValue('entity', cameraEntity);

      // 触发完成流程
      this.triggerFlow('onComplete');

      return {
        camera: camera,
        entity: cameraEntity
      };
    } catch (error) {
      console.error('创建正交相机失败:', error);
      throw error;
    }
  }
}

/**
 * 设置相机位置节点
 */
export class SetCameraPositionNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Entity',
      description: '相机实体'
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Vector3',
      defaultValue: { x: 0, y: 0, z: 5 },
      description: '相机位置'
    });

    // 输出插槽
    this.addOutput({
      name: 'onComplete',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成输出'
    });

    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Entity',
      description: '相机实体'
    });
  }

  public execute(): any {
    try {
      // 获取输入值
      const entity = this.getInputValue('entity');
      const position = this.getInputValue('position') || { x: 0, y: 0, z: 5 };

      if (!entity) {
        throw new Error('相机实体不能为空');
      }

      // 获取变换组件
      const transform = entity.getComponent(Transform);
      if (!transform) {
        throw new Error('相机实体缺少变换组件');
      }

      // 设置位置
      transform.setPosition(position.x, position.y, position.z);

      // 设置输出值
      this.setOutputValue('entity', entity);

      // 触发完成流程
      this.triggerFlow('onComplete');

      return { entity: entity };
    } catch (error) {
      console.error('设置相机位置失败:', error);
      throw error;
    }
  }
}

/**
 * 注册渲染节点
 * @param registry 节点注册表
 */
export function registerRenderingNodes(registry: NodeRegistry): void {
  // 注册创建透视相机节点
  registry.registerNodeType({
    type: 'rendering/camera/createPerspectiveCamera',
    category: NodeCategory.ENTITY,
    constructor: CreatePerspectiveCameraNode,
    label: '创建透视相机',
    description: '创建透视投影相机',
    icon: 'camera',
    color: '#FF5722',
    tags: ['rendering', 'camera', 'perspective']
  });

  // 注册创建正交相机节点
  registry.registerNodeType({
    type: 'rendering/camera/createOrthographicCamera',
    category: NodeCategory.ENTITY,
    constructor: CreateOrthographicCameraNode,
    label: '创建正交相机',
    description: '创建正交投影相机',
    icon: 'camera-orthographic',
    color: '#FF5722',
    tags: ['rendering', 'camera', 'orthographic']
  });

  // 注册设置相机位置节点
  registry.registerNodeType({
    type: 'rendering/camera/setCameraPosition',
    category: NodeCategory.ENTITY,
    constructor: SetCameraPositionNode,
    label: '设置相机位置',
    description: '设置相机在3D空间的位置',
    icon: 'camera-position',
    color: '#FF5722',
    tags: ['rendering', 'camera', 'position']
  });
}
