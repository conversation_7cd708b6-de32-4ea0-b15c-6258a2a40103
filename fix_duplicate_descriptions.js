const fs = require('fs');

function fixDuplicateDescriptions(filePath) {
  console.log(`修复文件: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 修复重复的 description 属性
  // 将第一个 description 改为 label，保留第二个作为 description
  content = content.replace(/(\s+)description: '([^']+)',\s*\n\s*description: '([^']+)',/g, 
    '$1label: \'$2\',\n$1description: \'$3\',');
  
  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`已修复: ${filePath}`);
}

// 修复 RenderingNodes.ts
fixDuplicateDescriptions('engine/src/visualscript/presets/RenderingNodes.ts');

console.log('重复描述修复完成！');
